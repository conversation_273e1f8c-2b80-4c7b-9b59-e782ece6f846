package cn.sdtbu.edu.controller;

import cn.sdtbu.edu.dto.FileUploadResponse;
import cn.sdtbu.edu.service.FileUploadService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * UploadController 测试类
 * <AUTHOR>
 */
@WebMvcTest(UploadController.class)
class UploadControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private FileUploadService fileUploadService;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMultipartFile testImageFile;
    private FileUploadResponse mockResponse;

    @BeforeEach
    void setUp() {
        // 创建测试图片文件
        testImageFile = new MockMultipartFile(
            "file",
            "test-avatar.jpg",
            MediaType.IMAGE_JPEG_VALUE,
            "test image content".getBytes()
        );

        // 创建模拟响应
        mockResponse = new FileUploadResponse(
            "avatar_123456789_001.jpg",
            "/uploads/avatars/avatar_123456789_001.jpg",
            testImageFile.getSize(),
            MediaType.IMAGE_JPEG_VALUE,
            System.currentTimeMillis()
        );
    }

    @Test
    void testUploadAvatar_Success() throws Exception {
        // 模拟服务层返回成功响应
        when(fileUploadService.uploadAvatar(any())).thenReturn(mockResponse);

        mockMvc.perform(multipart("/api/upload/avatar")
                .file(testImageFile))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("头像上传成功"))
                .andExpect(jsonPath("$.data.fileName").value("avatar_123456789_001.jpg"))
                .andExpect(jsonPath("$.data.fileUrl").value("/uploads/avatars/avatar_123456789_001.jpg"));
    }

    @Test
    void testUploadAvatar_EmptyFile() throws Exception {
        MockMultipartFile emptyFile = new MockMultipartFile(
            "file",
            "empty.jpg",
            MediaType.IMAGE_JPEG_VALUE,
            new byte[0]
        );

        mockMvc.perform(multipart("/api/upload/avatar")
                .file(emptyFile))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("请选择要上传的文件"));
    }

    @Test
    void testUploadAvatar_InvalidFileType() throws Exception {
        // 模拟服务层抛出文件类型错误
        when(fileUploadService.uploadAvatar(any()))
            .thenThrow(new IllegalArgumentException("只支持JPG、PNG、GIF格式的图片文件"));

        mockMvc.perform(multipart("/api/upload/avatar")
                .file(testImageFile))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("只支持JPG、PNG、GIF格式的图片文件"));
    }
}
