spring.application.name=spring-ai
spring.ai.openai.api-key=sk-lleozqhffqyvzlrfypagnsdfkssewmtjsvcfrpmspokgxadw
spring.ai.openai.chat.base-url=https://api.siliconflow.cn/
#spring.ai.openai.chat.options.model=Qwen/Qwen3-8B
spring.ai.openai.chat.options.model=deepseek-ai/DeepSeek-R1-Distill-Qwen-7B

#spring.ai.openai.chat.base-url = https://openrouter.ai/api
#spring.ai.openai.api-key=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
#spring.ai.openai.chat.options.model=qwen/qwen3-235b-a22b:free
##spring.ai.openai.chat.options.model=deepseek/deepseek-r1-0528:free

spring.ai.openai.chat.options.temperature=0.8
logging.level.org.springframework.ai.chat.client.advisor=DEBUG


spring.mail.host=smtp.qq.com
spring.mail.username=<EMAIL>
spring.mail.password=wmlxpdnfkektbaff
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true

spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=*******************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=wuyuhao123456#
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource

spring.datasource.druid.initial-size=5
spring.datasource.druid.min-idle=5
spring.datasource.druid.max-active=20
spring.datasource.druid.max-wait=60000
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.min-evictable-idle-time-millis=300000
spring.datasource.druid.validation-query=SELECT 1

spring.datasource.druid.stat-view-servlet.enabled=true
spring.datasource.druid.stat-view-servlet.url-pattern=/druid/*
spring.datasource.druid.stat-view-servlet.login-username=111111
spring.datasource.druid.stat-view-servlet.login-password=111111
spring.datasource.druid.stat-view-servlet.allow=127.0.0.1
spring.datasource.druid.stat-view-servlet.reset-enable=false

spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.pool-prepared-statements=true
spring.datasource.druid.max-pool-prepared-statement-per-connection-size=20
spring.datasource.druid.filters=stat,wall
spring.datasource.druid.connection-properties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000


spring.datasource.druid.web-stat-filter.enabled=true
spring.datasource.druid.web-stat-filter.url-pattern=/*
spring.datasource.druid.web-stat-filter.exclusions=*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*


mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.global-config.db-config.id-type=auto

server.port=8080

